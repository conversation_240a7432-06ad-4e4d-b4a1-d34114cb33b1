import simpleGit, { SimpleGit, LogResult } from 'simple-git';
import {
  GitCommit,
  GitBranches,
  GitCommitDetails,
  GitRepoInfo,
  GitFileChange,
  GitLogOptions
} from '../types/git';

export class GitService {
  private git: SimpleGit;
  private repoPath: string;

  constructor(repoPath: string = process.cwd()) {
    this.repoPath = repoPath;
    this.git = simpleGit(repoPath);
  }

  async isGitAvailable(): Promise<boolean> {
    try {
      await this.git.version();
      return true;
    } catch (error) {
      return false;
    }
  }

  async getCommits(limit: number = 50): Promise<GitCommit[]> {
    try {
      const log = await this.git.log({
        maxCount: limit,
        format: {
          hash: '%H',
          date: '%ai',
          message: '%s',
          author_name: '%an',
          author_email: '%ae',
          refs: '%D'
        }
      });

      return log.all.map(commit => ({
        hash: commit.hash,
        shortHash: commit.hash.substring(0, 7),
        message: commit.message,
        author: (commit as any).author_name || commit.author_name,
        email: (commit as any).author_email || commit.author_email,
        date: commit.date,
        refs: (commit as any).refs || ''
      }));
    } catch (error) {
      throw new Error(`Failed to get commits: ${(error as Error).message}`);
    }
  }

  async getBranches(): Promise<GitBranches> {
    try {
      const branches = await this.git.branch(['-a']);
      return {
        current: branches.current,
        all: branches.all,
        local: Object.keys(branches.branches).filter(name => !name.startsWith('remotes/')),
        remote: Object.keys(branches.branches).filter(name => name.startsWith('remotes/'))
      };
    } catch (error) {
      throw new Error(`Failed to get branches: ${(error as Error).message}`);
    }
  }

  async getCommitDetails(hash: string): Promise<GitCommitDetails> {
    try {
      const show = await this.git.show([hash, '--stat', '--format=fuller']);
      const diff = await this.git.diff([`${hash}^`, hash, '--name-status']);

      return {
        hash,
        details: show,
        files: this.parseFileDiff(diff)
      };
    } catch (error) {
      throw new Error(`Failed to get commit details: ${(error as Error).message}`);
    }
  }

  private parseFileDiff(diff: string): GitFileChange[] {
    const lines = diff.split('\n').filter(line => line.trim());
    return lines.map(line => {
      const [status, ...fileParts] = line.split('\t');
      const file = fileParts.join('\t');
      return {
        status: status.charAt(0),
        file: file,
        statusText: this.getStatusText(status.charAt(0))
      };
    });
  }

  private getStatusText(status: string): string {
    const statusMap: Record<string, string> = {
      'A': 'Added',
      'M': 'Modified',
      'D': 'Deleted',
      'R': 'Renamed',
      'C': 'Copied',
      'U': 'Unmerged'
    };
    return statusMap[status] || 'Unknown';
  }

  async getRepoInfo(): Promise<GitRepoInfo> {
    try {
      const status = await this.git.status();
      const remotes = await this.git.getRemotes(true);

      return {
        path: this.repoPath,
        current: status.current || 'main',
        ahead: status.ahead || 0,
        behind: status.behind || 0,
        staged: status.staged.length,
        modified: status.modified.length,
        untracked: status.not_added.length,
        remotes: remotes
      };
    } catch (error) {
      throw new Error(`Failed to get repo info: ${(error as Error).message}`);
    }
  }

  async getCommitGraph(limit: number = 50): Promise<GitCommit[]> {
    try {
      const log = await this.git.raw([
        'log',
        '--graph',
        '--pretty=format:%H|%P|%s|%an|%ad|%D',
        '--date=iso',
        `--max-count=${limit}`,
        '--all'
      ]);

      const lines = log.split('\n');
      const commits: GitCommit[] = [];
      const graphLines: string[] = [];

      // First pass: collect all lines and separate graph from commit data
      lines.forEach((line) => {
        if (line.trim()) {
          graphLines.push(line);
        }
      });

      // Second pass: parse commits and their graph information
      graphLines.forEach((line, index) => {
        // Look for lines that contain commit data (have pipe separators)
        if (line.includes('|') && line.split('|').length >= 4) {
          // Find where the graph part ends and data begins
          const match = line.match(/^([\s\*\|\/\\\-_]+)\s+(.+)$/);
          if (!match) return;

          const graphPart = match[1];
          const dataPart = match[2];
          const parts = dataPart.split('|');

          // Ensure we have all required parts with fallbacks
          const hash = (parts[0] || '').trim();
          const parents = (parts[1] || '').trim();
          const message = (parts[2] || 'No commit message').trim();
          const author = (parts[3] || 'Unknown Author').trim();
          const date = (parts[4] || new Date().toISOString()).trim();
          const refs = (parts[5] || '').trim();

          // Only add commit if we have a valid hash (40 character SHA)
          if (hash && hash.length === 40 && /^[a-f0-9]+$/i.test(hash)) {
            const parsedGraph = this.parseGraphStructure(graphPart, index, graphLines);

            commits.push({
              hash,
              shortHash: hash.substring(0, 7),
              parents: parents ? parents.split(' ').filter(p => p.trim()) : [],
              message,
              author,
              email: '', // Email not available in graph format
              date,
              refs: refs || '',
              graph: graphPart,
              level: parsedGraph.level,
              ...parsedGraph
            });
          }
        }
      });

      return commits;
    } catch (error) {
      throw new Error(`Failed to get commit graph: ${(error as Error).message}`);
    }
  }

  private calculateLevel(graphPart: string): number {
    // Simple level calculation based on graph characters
    let level = 0;
    for (const char of graphPart) {
      if (char === '|' || char === '*' || char === '\\' || char === '/') {
        level++;
      }
    }
    return Math.max(0, level - 1);
  }

  async isValidRepository(): Promise<boolean> {
    try {
      // First check if git is available
      const gitAvailable = await this.isGitAvailable();
      if (!gitAvailable) {
        return false;
      }

      // Then check if this is a valid git repository
      await this.git.status();
      return true;
    } catch {
      return false;
    }
  }
}
