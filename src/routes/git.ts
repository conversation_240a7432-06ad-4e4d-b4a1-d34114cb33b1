import { Router, Request, Response } from 'express';
import { GitService } from '../services/gitService';
import { ApiResponse } from '../types/git';
import fs from 'fs';
import path from 'path';
import os from 'os';

const router = Router();

// Initialize Git service with default path
let gitService = new GitService();

// Get repository information
router.get('/info', async (req: Request, res: Response): Promise<void> => {
  try {
    // Check if git is available first
    const gitAvailable = await gitService.isGitAvailable();
    if (!gitAvailable) {
      res.status(500).json({
        success: false,
        error: 'Git is not available. Please ensure Git is installed and accessible in your PATH.'
      } as ApiResponse<null>);
      return;
    }

    const isValid = await gitService.isValidRepository();
    if (!isValid) {
      res.status(400).json({
        success: false,
        error: 'Not a valid Git repository'
      } as ApiResponse<null>);
      return;
    }

    const info = await gitService.getRepoInfo();
    res.json({
      success: true,
      data: info
    } as ApiResponse<typeof info>);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: (error as Error).message
    } as ApiResponse<null>);
  }
});

// Get commits
router.get('/commits', async (req: Request, res: Response) => {
  try {
    const limit = parseInt(req.query.limit as string) || 50;
    const commits = await gitService.getCommits(limit);

    res.json({
      success: true,
      data: commits
    } as ApiResponse<typeof commits>);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: (error as Error).message
    } as ApiResponse<null>);
  }
});

// Get commit graph
router.get('/commits/graph', async (req: Request, res: Response) => {
  try {
    // Check if git is available first
    const gitAvailable = await gitService.isGitAvailable();
    if (!gitAvailable) {
      res.status(500).json({
        success: false,
        error: 'Git is not available. Please ensure Git is installed and accessible in your PATH.'
      } as ApiResponse<null>);
      return;
    }

    const limit = parseInt(req.query.limit as string) || 50;
    const commits = await gitService.getCommitGraph(limit);

    res.json({
      success: true,
      data: commits
    } as ApiResponse<typeof commits>);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: (error as Error).message
    } as ApiResponse<null>);
  }
});

// Get commit details
router.get('/commits/:hash', async (req: Request, res: Response) => {
  try {
    const { hash } = req.params;
    const details = await gitService.getCommitDetails(hash);

    res.json({
      success: true,
      data: details
    } as ApiResponse<typeof details>);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: (error as Error).message
    } as ApiResponse<null>);
  }
});

// Get branches
router.get('/branches', async (req: Request, res: Response) => {
  try {
    // Check if git is available first
    const gitAvailable = await gitService.isGitAvailable();
    if (!gitAvailable) {
      res.status(500).json({
        success: false,
        error: 'Git is not available. Please ensure Git is installed and accessible in your PATH.'
      } as ApiResponse<null>);
      return;
    }

    const branches = await gitService.getBranches();

    res.json({
      success: true,
      data: branches
    } as ApiResponse<typeof branches>);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: (error as Error).message
    } as ApiResponse<null>);
  }
});

// Change repository path
router.post('/change-repo', async (req: Request, res: Response): Promise<void> => {
  try {
    const { repoPath } = req.body;

    if (!repoPath) {
      res.status(400).json({
        success: false,
        error: 'Repository path is required'
      } as ApiResponse<null>);
      return;
    }

    // Check if path exists
    if (!fs.existsSync(repoPath)) {
      res.status(400).json({
        success: false,
        error: 'Path does not exist'
      } as ApiResponse<null>);
      return;
    }

    // Create new GitService instance with the new path
    gitService = new GitService(repoPath);

    // Verify it's a valid Git repository
    const isValid = await gitService.isValidRepository();
    if (!isValid) {
      res.status(400).json({
        success: false,
        error: 'Not a valid Git repository'
      } as ApiResponse<null>);
      return;
    }

    const info = await gitService.getRepoInfo();
    res.json({
      success: true,
      data: info
    } as ApiResponse<typeof info>);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: (error as Error).message
    } as ApiResponse<null>);
  }
});

// Browse directories
router.get('/browse', async (req: Request, res: Response): Promise<void> => {
  try {
    const { dirPath } = req.query;
    let targetPath = dirPath as string;

    // If no path provided, use current working directory
    if (!targetPath) {
      targetPath = process.cwd();
    }

    if (!fs.existsSync(targetPath)) {
      res.status(400).json({
        success: false,
        error: 'Directory does not exist'
      } as ApiResponse<null>);
      return;
    }

    const stats = fs.statSync(targetPath);
    if (!stats.isDirectory()) {
      res.status(400).json({
        success: false,
        error: 'Path is not a directory'
      } as ApiResponse<null>);
      return;
    }

    const items = fs.readdirSync(targetPath)
      .filter(item => !item.startsWith('.') || item === '.git')
      .map(item => {
        const itemPath = path.join(targetPath, item);
        const itemStats = fs.statSync(itemPath);
        // const isGitRepo = itemStats.isDirectory() && fs.existsSync(path.join(itemPath, '.git'));
        const isGitRepo = itemStats.isDirectory() && fs.existsSync(path.join(itemPath, '.git'));

        return {
          name: item,
          path: itemPath,
          isDirectory: itemStats.isDirectory(),
          isGitRepo,
          size: itemStats.size,
          modified: itemStats.mtime
        };
      })
      .sort((a, b) => {
        // Directories first, then files
        if (a.isDirectory && !b.isDirectory) return -1;
        if (!a.isDirectory && b.isDirectory) return 1;
        // Git repos first among directories
        if (a.isDirectory && b.isDirectory) {
          if (a.isGitRepo && !b.isGitRepo) return -1;
          if (!a.isGitRepo && b.isGitRepo) return 1;
        }
        return a.name.localeCompare(b.name);
      });

    // Add parent directory option if not at root
    const parentPath = path.dirname(targetPath);
    if (parentPath !== targetPath) {
      items.unshift({
        name: '..',
        path: parentPath,
        isDirectory: true,
        isGitRepo: false,
        size: 0,
        modified: new Date()
      });
    }

    res.json({
      success: true,
      data: {
        currentPath: targetPath,
        items
      }
    } as ApiResponse<{ currentPath: string; items: any[] }>);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: (error as Error).message
    } as ApiResponse<null>);
  }
});

// Get user home directory
router.get('/home', async (req: Request, res: Response): Promise<void> => {
  try {
    const homeDir = os.homedir();
    res.json({
      success: true,
      data: { homeDir }
    } as ApiResponse<{ homeDir: string }>);
  } catch (error) {
    res.status(500).json({
      success: false,
      error: (error as Error).message
    } as ApiResponse<null>);
  }
});

export default router;
