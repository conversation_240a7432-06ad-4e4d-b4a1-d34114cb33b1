// Types
interface GitCommit {
    hash: string;
    shortHash: string;
    message: string;
    author: string;
    email: string;
    date: string;
    refs: string;
    parents?: string[];
    graph?: string;
    level?: number;
}

interface GitRepoInfo {
    path: string;
    current: string;
    ahead: number;
    behind: number;
    staged: number;
    modified: number;
    untracked: number;
    remotes: any[];
}

interface GitBranches {
    current: string;
    all: string[];
    local: string[];
    remote: string[];
}

interface ApiResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
}

class GitViewer {
    private commits: GitCommit[] = [];
    private repoInfo: GitRepoInfo | null = null;
    private branches: GitBranches | null = null;
    private currentBrowserPath: string = '';

    constructor() {
        this.init();
    }

    private async init(): Promise<void> {
        this.setupEventListeners();
        await this.loadData();
    }

    private setupEventListeners(): void {
        // Refresh button
        const refreshBtn = document.getElementById('refreshBtn');
        refreshBtn?.addEventListener('click', () => this.loadData());

        // Change repository button
        const changeRepoBtn = document.getElementById('changeRepoBtn');
        changeRepoBtn?.addEventListener('click', () => this.openRepoBrowser());

        // Commit limit selector
        const commitLimit = document.getElementById('commitLimit') as HTMLSelectElement;
        commitLimit?.addEventListener('change', () => this.loadCommits());

        // Modal close buttons
        const modalClose = document.getElementById('modalClose');
        const modal = document.getElementById('commitModal');
        modalClose?.addEventListener('click', () => this.closeModal());
        modal?.addEventListener('click', (e) => {
            if (e.target === modal) this.closeModal();
        });

        // Repository browser modal
        const repoBrowserClose = document.getElementById('repoBrowserClose');
        const repoBrowserModal = document.getElementById('repoBrowserModal');
        repoBrowserClose?.addEventListener('click', () => this.closeRepoBrowser());
        repoBrowserModal?.addEventListener('click', (e) => {
            if (e.target === repoBrowserModal) this.closeRepoBrowser();
        });

        // Repository browser controls
        const homeBtn = document.getElementById('homeBtn');
        const parentBtn = document.getElementById('parentBtn');
        const selectManualPath = document.getElementById('selectManualPath');
        const manualPath = document.getElementById('manualPath') as HTMLInputElement;

        homeBtn?.addEventListener('click', () => this.browseToHome());
        parentBtn?.addEventListener('click', () => this.browseToParent());
        selectManualPath?.addEventListener('click', () => this.selectManualPath());
        manualPath?.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') this.selectManualPath();
        });

        // Error toast close
        const errorToastClose = document.getElementById('errorToastClose');
        errorToastClose?.addEventListener('click', () => this.hideError());

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeModal();
                this.closeRepoBrowser();
            }
            if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
                e.preventDefault();
                this.loadData();
            }
        });
    }

    private async loadData(): Promise<void> {
        try {
            await Promise.all([
                this.loadRepoInfo(),
                this.loadBranches(),
                this.loadCommits()
            ]);
        } catch (error) {
            this.showError('Failed to load repository data');
            console.error('Error loading data:', error);
        }
    }

    private async loadRepoInfo(): Promise<void> {
        try {
            const response = await fetch('/api/git/info');
            const result: ApiResponse<GitRepoInfo> = await response.json();

            if (result.success && result.data) {
                this.repoInfo = result.data;
                this.updateRepoInfoUI();
            } else {
                throw new Error(result.error || 'Failed to load repository info');
            }
        } catch (error) {
            console.error('Error loading repo info:', error);
            throw error;
        }
    }

    private async loadBranches(): Promise<void> {
        try {
            const response = await fetch('/api/git/branches');
            const result: ApiResponse<GitBranches> = await response.json();

            if (result.success && result.data) {
                this.branches = result.data;
                this.updateBranchesUI();
            } else {
                throw new Error(result.error || 'Failed to load branches');
            }
        } catch (error) {
            console.error('Error loading branches:', error);
            throw error;
        }
    }

    private async loadCommits(): Promise<void> {
        try {
            const commitLimit = document.getElementById('commitLimit') as HTMLSelectElement;
            const limit = parseInt(commitLimit?.value || '50');

            // Show loading state
            const commitList = document.getElementById('commitList');
            if (commitList) {
                commitList.innerHTML = `
                    <div class="loading">
                        <i class="fas fa-spinner fa-spin"></i>
                        Loading commits...
                    </div>
                `;
            }

            const response = await fetch(`/api/git/commits/graph?limit=${limit}`);
            const result: ApiResponse<GitCommit[]> = await response.json();

            if (result.success && result.data) {
                this.commits = result.data;
                this.updateCommitsUI();
                this.drawCommitGraph();
            } else {
                throw new Error(result.error || 'Failed to load commits');
            }
        } catch (error) {
            console.error('Error loading commits:', error);
            const commitList = document.getElementById('commitList');
            if (commitList) {
                commitList.innerHTML = `
                    <div class="loading">
                        <i class="fas fa-exclamation-triangle"></i>
                        Failed to load commits
                    </div>
                `;
            }
            throw error;
        }
    }

    private updateRepoInfoUI(): void {
        if (!this.repoInfo) return;

        // Update repo path
        const repoPath = document.getElementById('repoPath');
        if (repoPath) {
            const pathSpan = repoPath.querySelector('span');
            if (pathSpan) {
                pathSpan.textContent = this.repoInfo.path;
            }
        }

        // Update current branch
        const currentBranch = document.getElementById('currentBranch');
        if (currentBranch) {
            const branchSpan = currentBranch.querySelector('span');
            if (branchSpan) {
                branchSpan.textContent = this.repoInfo.current;
            }
        }

        // Update changes count
        const repoChanges = document.getElementById('repoChanges');
        if (repoChanges) {
            const changesSpan = repoChanges.querySelector('span');
            const totalChanges = this.repoInfo.staged + this.repoInfo.modified + this.repoInfo.untracked;
            if (changesSpan) {
                changesSpan.textContent = `${totalChanges} changes`;
            }
        }

        // Update stats
        this.updateElement('aheadCount', this.repoInfo.ahead.toString());
        this.updateElement('behindCount', this.repoInfo.behind.toString());
        this.updateElement('stagedCount', this.repoInfo.staged.toString());
        this.updateElement('modifiedCount', this.repoInfo.modified.toString());
    }

    private updateBranchesUI(): void {
        if (!this.branches) return;

        const branchesList = document.getElementById('branchesList');
        if (!branchesList) return;

        const localBranches = this.branches.local.map(branch => `
            <div class="branch-item ${branch === this.branches!.current ? 'current' : ''}" data-branch="${branch}">
                <i class="fas fa-code-branch"></i>
                <span>${branch}</span>
                ${branch === this.branches!.current ? '<i class="fas fa-check"></i>' : ''}
            </div>
        `).join('');

        branchesList.innerHTML = localBranches;

        // Add click handlers for branch items
        branchesList.querySelectorAll('.branch-item').forEach(item => {
            item.addEventListener('click', () => {
                const branchName = item.getAttribute('data-branch');
                if (branchName) {
                    this.showInfo(`Branch switching not implemented yet: ${branchName}`);
                }
            });
        });
    }

    private updateCommitsUI(): void {
        const commitList = document.getElementById('commitList');
        if (!commitList) return;

        if (this.commits.length === 0) {
            commitList.innerHTML = `
                <div class="loading">
                    <i class="fas fa-info-circle"></i>
                    No commits found
                </div>
            `;
            return;
        }

        const commitsHtml = this.commits.map(commit => {
            const date = new Date(commit.date);
            const formattedDate = date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
            const authorInitials = (commit.author || 'Unknown').split(' ').map(n => n[0]).join('').toUpperCase();

            const refs = commit.refs ? commit.refs.split(', ').map(ref => {
                const cleanRef = ref.replace(/origin\/|HEAD -> /, '');
                return `<span class="ref-tag">${cleanRef}</span>`;
            }).join('') : '';

            return `
                <div class="commit-item" data-hash="${commit.hash}">
                    <div class="commit-avatar">${authorInitials}</div>
                    <div class="commit-info">
                        <div class="commit-message">${this.escapeHtml(commit.message)}</div>
                        <div class="commit-meta">
                            <span>${commit.author}</span>
                            <span>${formattedDate}</span>
                            <span class="commit-hash">${commit.shortHash}</span>
                        </div>
                        ${refs ? `<div class="commit-refs">${refs}</div>` : ''}
                    </div>
                </div>
            `;
        }).join('');

        commitList.innerHTML = commitsHtml;

        // Add click handlers for commit items
        commitList.querySelectorAll('.commit-item').forEach(item => {
            item.addEventListener('click', () => {
                const hash = item.getAttribute('data-hash');
                if (hash) {
                    this.showCommitDetails(hash);
                }
            });
        });
    }

    private drawCommitGraph(): void {
        const canvas = document.getElementById('graphCanvas') as HTMLCanvasElement;
        const loading = document.querySelector('.graph-loading') as HTMLElement;

        if (!canvas || !this.commits.length) return;

        // Hide loading
        if (loading) loading.style.display = 'none';

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        // Set canvas size
        const rect = canvas.getBoundingClientRect();
        canvas.width = rect.width * window.devicePixelRatio;
        canvas.height = rect.height * window.devicePixelRatio;
        ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

        // Clear canvas
        ctx.clearRect(0, 0, rect.width, rect.height);

        // Graph settings
        const nodeRadius = 4;
        const rowHeight = 25;
        const colWidth = 20;
        const startX = 30;
        const startY = 20;

        // Colors for different branches
        const colors = ['#7c3aed', '#f59e0b', '#10b981', '#ef4444', '#3b82f6', '#8b5cf6'];

        // Draw commits
        this.commits.forEach((commit, index) => {
            const x = startX + (commit.level || 0) * colWidth;
            const y = startY + index * rowHeight;
            const color = colors[(commit.level || 0) % colors.length];

            // Draw connection lines to parents
            if (commit.parents && commit.parents.length > 0) {
                commit.parents.forEach(parentHash => {
                    const parentIndex = this.commits.findIndex(c => c.hash === parentHash);
                    if (parentIndex !== -1 && parentIndex > index) {
                        const parentCommit = this.commits[parentIndex];
                        const parentX = startX + (parentCommit.level || 0) * colWidth;
                        const parentY = startY + parentIndex * rowHeight;

                        ctx.strokeStyle = color;
                        ctx.lineWidth = 2;
                        ctx.beginPath();
                        ctx.moveTo(x, y);
                        ctx.lineTo(parentX, parentY);
                        ctx.stroke();
                    }
                });
            }

            // Draw commit node
            ctx.fillStyle = color;
            ctx.beginPath();
            ctx.arc(x, y, nodeRadius, 0, 2 * Math.PI);
            ctx.fill();

            // Draw commit message
            ctx.fillStyle = '#e6edf3';
            ctx.font = '12px Inter, sans-serif';
            ctx.fillText(
                commit.message.length > 50 ? commit.message.substring(0, 50) + '...' : commit.message,
                x + 15,
                y + 4
            );

            // Draw commit hash
            ctx.fillStyle = '#7d8590';
            ctx.font = '10px SF Mono, monospace';
            ctx.fillText(commit.shortHash, x + 15, y - 8);
        });
    }

    private async showCommitDetails(hash: string): Promise<void> {
        const modal = document.getElementById('commitModal');
        const modalTitle = document.getElementById('modalTitle');
        const modalBody = document.getElementById('modalBody');

        if (!modal || !modalTitle || !modalBody) return;

        // Show modal with loading state
        modal.classList.add('show');
        modalTitle.textContent = `Commit ${hash.substring(0, 7)}`;
        modalBody.innerHTML = `
            <div class="loading">
                <i class="fas fa-spinner fa-spin"></i>
                Loading commit details...
            </div>
        `;

        try {
            const response = await fetch(`/api/git/commits/${hash}`);
            const result: ApiResponse<any> = await response.json();

            if (result.success && result.data) {
                const { details, files } = result.data;

                const filesHtml = files.map((file: any) => `
                    <div class="file-change">
                        <span class="file-status status-${file.status.toLowerCase()}">${file.statusText}</span>
                        <span class="file-name">${file.file}</span>
                    </div>
                `).join('');

                modalBody.innerHTML = `
                    <div class="commit-details">
                        <h4>Changed Files</h4>
                        <div class="files-list">
                            ${filesHtml || '<p>No files changed</p>'}
                        </div>
                        <h4>Commit Details</h4>
                        <pre class="commit-diff">${this.escapeHtml(details)}</pre>
                    </div>
                `;
            } else {
                throw new Error(result.error || 'Failed to load commit details');
            }
        } catch (error) {
            modalBody.innerHTML = `
                <div class="error">
                    <i class="fas fa-exclamation-triangle"></i>
                    Failed to load commit details: ${(error as Error).message}
                </div>
            `;
        }
    }

    private closeModal(): void {
        const modal = document.getElementById('commitModal');
        modal?.classList.remove('show');
    }

    private showError(message: string): void {
        const errorToast = document.getElementById('errorToast');
        const errorMessage = document.getElementById('errorMessage');

        if (errorToast && errorMessage) {
            errorMessage.textContent = message;
            errorToast.classList.add('show');

            // Auto hide after 5 seconds
            setTimeout(() => this.hideError(), 5000);
        }
    }

    private hideError(): void {
        const errorToast = document.getElementById('errorToast');
        errorToast?.classList.remove('show');
    }



    private updateElement(id: string, text: string): void {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = text;
        }
    }

    private escapeHtml(text: string): string {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Repository Browser Methods
    private async openRepoBrowser(): Promise<void> {
        const modal = document.getElementById('repoBrowserModal');
        modal?.classList.add('show');
        await this.loadDirectoryBrowser();
    }

    private closeRepoBrowser(): void {
        const modal = document.getElementById('repoBrowserModal');
        modal?.classList.remove('show');
    }

    private async loadDirectoryBrowser(path?: string): Promise<void> {
        try {
            let targetPath = path || this.currentBrowserPath;

            // If no path is set, don't pass any parameter to use server default
            const url = targetPath ? `/api/git/browse?dirPath=${encodeURIComponent(targetPath)}` : '/api/git/browse';
            const response = await fetch(url);
            const result: ApiResponse<{ currentPath: string; items: any[] }> = await response.json();

            if (result.success && result.data) {
                this.currentBrowserPath = result.data.currentPath;
                this.updateBrowserUI(result.data);
            } else {
                throw new Error(result.error || 'Failed to load directory');
            }
        } catch (error) {
            this.showError(`Failed to load directory: ${(error as Error).message}`);
        }
    }

    private updateBrowserUI(data: { currentPath: string; items: any[] }): void {
        const pathText = document.getElementById('pathText');
        const browserContent = document.getElementById('browserContent');
        const manualPath = document.getElementById('manualPath') as HTMLInputElement;

        if (pathText) {
            pathText.textContent = data.currentPath;
        }

        if (manualPath) {
            manualPath.value = data.currentPath;
        }

        if (browserContent) {
            const itemsHtml = data.items.map(item => {
                const iconClass = item.isGitRepo ? 'git' : (item.isDirectory ? 'folder' : 'file');
                const iconName = item.isGitRepo ? 'fa-git-alt' : (item.isDirectory ? 'fa-folder' : 'fa-file');
                const gitBadge = item.isGitRepo ? '<span class="git-badge">Git Repo</span>' : '';
                const size = item.isDirectory ? '' : this.formatFileSize(item.size);
                const date = new Date(item.modified).toLocaleDateString();

                return `
                    <div class="directory-item ${item.isGitRepo ? 'git-repo' : ''}" data-path="${item.path}" data-is-directory="${item.isDirectory}" data-is-git="${item.isGitRepo}">
                        <i class="directory-icon ${iconClass} fas ${iconName}"></i>
                        <div class="directory-info">
                            <div class="directory-name">${this.escapeHtml(item.name)}</div>
                            <div class="directory-meta">${size} ${date}</div>
                        </div>
                        ${gitBadge}
                    </div>
                `;
            }).join('');

            browserContent.innerHTML = itemsHtml;

            // Add click handlers
            browserContent.querySelectorAll('.directory-item').forEach(item => {
                item.addEventListener('click', () => {
                    const path = item.getAttribute('data-path');
                    const isDirectory = item.getAttribute('data-is-directory') === 'true';
                    const isGit = item.getAttribute('data-is-git') === 'true';

                    if (path) {
                        if (isGit) {
                            this.selectRepository(path);
                        } else if (isDirectory) {
                            this.loadDirectoryBrowser(path);
                        }
                    }
                });

                // Double-click to select any directory as repo
                item.addEventListener('dblclick', () => {
                    const path = item.getAttribute('data-path');
                    const isDirectory = item.getAttribute('data-is-directory') === 'true';
                    if (path && isDirectory) {
                        this.selectRepository(path);
                    }
                });
            });
        }
    }

    private async selectRepository(path: string): Promise<void> {
        try {
            const response = await fetch('/api/git/change-repo', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ repoPath: path })
            });

            const result: ApiResponse<GitRepoInfo> = await response.json();

            if (result.success) {
                this.closeRepoBrowser();
                await this.loadData();
                this.showInfo(`Successfully switched to repository: ${path}`);
            } else {
                throw new Error(result.error || 'Failed to change repository');
            }
        } catch (error) {
            this.showError(`Failed to change repository: ${(error as Error).message}`);
        }
    }

    private async browseToHome(): Promise<void> {
        try {
            const response = await fetch('/api/git/home');
            const result: ApiResponse<{ homeDir: string }> = await response.json();

            if (result.success && result.data) {
                await this.loadDirectoryBrowser(result.data.homeDir);
            } else {
                // Fallback to root directory
                await this.loadDirectoryBrowser('/');
            }
        } catch (error) {
            // Fallback to root directory
            await this.loadDirectoryBrowser('/');
        }
    }

    private async browseToParent(): Promise<void> {
        if (this.currentBrowserPath) {
            const parentPath = this.currentBrowserPath.split('/').slice(0, -1).join('/') || '/';
            await this.loadDirectoryBrowser(parentPath);
        }
    }

    private async selectManualPath(): Promise<void> {
        const manualPath = document.getElementById('manualPath') as HTMLInputElement;
        if (manualPath && manualPath.value.trim()) {
            await this.loadDirectoryBrowser(manualPath.value.trim());
        }
    }

    private formatFileSize(bytes: number): string {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    private showInfo(message: string): void {
        // Simple alert for now - could be replaced with a proper info toast
        console.log('Info:', message);
        // You could implement a proper info toast here
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new GitViewer();
});
